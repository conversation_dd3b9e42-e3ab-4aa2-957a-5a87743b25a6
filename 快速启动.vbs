' ================================================================
' 🏷️ 邮件系统 2.0 版本 - 快速启动图形界面
' 版本: 2.1 (修复闪退版)
' 系统: 2.0 完整功能版本 ⭐
' 功能: 稳定启动邮件系统GUI (包含所有2.0原始功能)
' 更新: 2025-06-12 - 修复闪退问题
'
' 📋 2.0系统特色功能：
' • 完整的撤回功能
' • 自动回复监控
' • 质量数据库管理
' • 应急管理系统
' • 深度协调系统
' • 智能检索功能
' ================================================================
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 检查主脚本文件
Dim scriptPath
scriptPath = currentDir & "\gui_main.py"

If Not objFSO.FileExists(scriptPath) Then
    MsgBox "错误：找不到gui_main.py文件！" & vbCrLf & vbCrLf & _
           "当前目录：" & currentDir & vbCrLf & vbCrLf & _
           "请确保gui_main.py文件在同一目录中。", vbCritical, "文件不存在"
    WScript.Quit
End If

' 启动图形界面
On Error Resume Next
Dim command, result

' 构建启动命令
command = "cmd /c ""cd /d """ & currentDir & """ && python gui_main.py"""

' 尝试启动
result = objShell.Run(command, 1, False)

' 检查是否有错误
If Err.Number <> 0 Then
    ' 如果有错误，尝试python3
    Err.Clear
    command = "cmd /c ""cd /d """ & currentDir & """ && python3 gui_main.py"""
    result = objShell.Run(command, 1, False)

    ' 如果还是有错误，显示错误信息
    If Err.Number <> 0 Then
        On Error GoTo 0
        MsgBox "启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & vbCrLf & _
               "请尝试以下解决方案：" & vbCrLf & _
               "1. 手动运行：python gui_main.py" & vbCrLf & _
               "2. 检查Python是否正确安装" & vbCrLf & _
               "3. 检查Python是否添加到PATH环境变量" & vbCrLf & _
               "4. 尝试重新安装Python", vbCritical, "启动错误"
        WScript.Quit
    End If
End If

On Error GoTo 0

' 如果没有错误，显示启动信息（可选）
' MsgBox "邮件系统正在启动..." & vbCrLf & "请稍等片刻。", vbInformation, "启动中"
